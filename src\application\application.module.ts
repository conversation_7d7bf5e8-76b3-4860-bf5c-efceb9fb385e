/**
 * Simplified Application Module
 *
 * Clean, maintainable module for application management.
 * Removed complex abstractions and dependencies for better maintainability.
 */

import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PrismaService } from '../utils/prisma.service';
import { LoggerService } from '../utils/logger.service';

// Media and storage services
import { MediaService } from '../media/media.service';
import { SupabaseService } from '../utils/supabase.service';

// Simplified services
import { ApplicationService } from './application.service';
import { ApplicationIntegrationService } from './services/application-integration.service';
import { ApplicationFormService } from './services/application-form.service';
import { ApplicationDocumentService } from './services/application-document.service';
import { ApplicationTransformerService } from './services/application-transformer.service';
import { DocumentVaultService } from './services/document-vault.service';
import { NotificationService } from './services/notification.service';
import { MailerService } from '../mailer/mailer.service';

// Controllers
import { ApplicationController } from './controllers/application.controller';
import { NotificationController } from './controllers/notification.controller';

/**
 * Simplified Application Module
 *
 * Provides clean, maintainable application management functionality.
 * Removed complex abstractions for better maintainability.
 */
@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default-secret',
      signOptions: { expiresIn: '24h' },
    }),
  ],
  controllers: [ApplicationController, NotificationController],
  providers: [
    // Core infrastructure services
    PrismaService,
    LoggerService,

    // Media and storage services
    MediaService,
    SupabaseService,

    // Simplified application services
    ApplicationService,
    ApplicationIntegrationService,
    ApplicationFormService,
    ApplicationDocumentService,
    ApplicationTransformerService,
    DocumentVaultService,
    NotificationService,
    MailerService,
  ],
  exports: [
    // Export services for use in other modules
    ApplicationService,
    ApplicationIntegrationService,
    ApplicationFormService,
    ApplicationDocumentService,
    ApplicationTransformerService,
    DocumentVaultService,

    // Export media services
    MediaService,
    SupabaseService,

    // Export infrastructure services
    PrismaService,
    LoggerService,
  ],
})
export class ApplicationModule {}
